"use client"

import { useState, useEffect } from 'react';
import { StatementService } from '@/lib/supabase';
import type { Statement } from '@/types/database';

interface CreateStatementRequest {
  content: string;
  officer_notes?: string;
}

interface UseStatementResult {
  statement: Statement | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createOrUpdateStatement: (statementData: CreateStatementRequest) => Promise<Statement>;
}

export function useStatement(interviewId: string): UseStatementResult {
  const [statement, setStatement] = useState<Statement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatement = async () => {
    try {
      setLoading(true);
      setError(null);

      const statementResult = await StatementService.getStatement(interviewId);
      setStatement(statementResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setStatement(null);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateStatement = async (statementData: CreateStatementRequest): Promise<Statement> => {
    try {
      setError(null);

      // Check if statement already exists
      const existingStatement = await StatementService.getStatement(interviewId);

      let updatedStatement: Statement;
      if (existingStatement) {
        // Update existing statement
        updatedStatement = await StatementService.updateStatement(interviewId, statementData);
      } else {
        // Create new statement
        updatedStatement = await StatementService.createStatement(interviewId, statementData.content, statementData.officer_notes);
      }

      setStatement(updatedStatement);
      return updatedStatement;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchStatement();
    }
  }, [interviewId]);

  return {
    statement,
    loading,
    error,
    refetch: fetchStatement,
    createOrUpdateStatement,
  };
}
