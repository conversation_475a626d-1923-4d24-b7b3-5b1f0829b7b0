"use client"

import { useState, useEffect } from 'react';
import { TranscriptionService } from '@/lib/supabase';
import type { Transcription, CreateTranscriptionRequest } from '@/types/database';

interface UseTranscriptionResult {
  transcription: Transcription | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createOrUpdateTranscription: (transcriptionData: CreateTranscriptionRequest) => Promise<Transcription>;
}

export function useTranscription(interviewId: string): UseTranscriptionResult {
  const [transcription, setTranscription] = useState<Transcription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTranscription = async () => {
    try {
      setLoading(true);
      setError(null);

      const transcriptionResult = await TranscriptionService.getTranscription(interviewId);
      setTranscription(transcriptionResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setTranscription(null);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateTranscription = async (transcriptionData: CreateTranscriptionRequest): Promise<Transcription> => {
    try {
      setError(null);

      // Check if transcription already exists
      const existingTranscription = await TranscriptionService.getTranscription(interviewId);

      let updatedTranscription: Transcription;
      if (existingTranscription) {
        // Update existing transcription
        updatedTranscription = await TranscriptionService.updateTranscription(interviewId, transcriptionData);
      } else {
        // Create new transcription - need to adapt the parameters for the service
        updatedTranscription = await TranscriptionService.createTranscription(
          interviewId,
          transcriptionData.transcription_data,
          'Officer', // TODO: Get actual officer name
          'Witness', // TODO: Get actual witness name
          transcriptionData.language || 'en-US'
        );
      }

      setTranscription(updatedTranscription);
      return updatedTranscription;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchTranscription();
    }
  }, [interviewId]);

  return {
    transcription,
    loading,
    error,
    refetch: fetchTranscription,
    createOrUpdateTranscription,
  };
}
