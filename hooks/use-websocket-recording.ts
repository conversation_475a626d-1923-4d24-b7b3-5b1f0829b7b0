"use client"

import { useState, useEffect, useRef, useCallback } from 'react';
import type { TranscriptData, TranscriptSegment, Speaker } from '@/types/database';

interface WebSocketRecordingConfig {
  websocketUrl: string;
  sampleRate?: number;
  bufferSize?: number;
  onTranscriptionUpdate?: (data: TranscriptData) => void;
  onError?: (error: string) => void;
  onConnectionChange?: (connected: boolean) => void;
}

interface UseWebSocketRecordingResult {
  isConnected: boolean;
  isRecording: boolean;
  transcriptData: TranscriptData | null;
  error: string | null;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  clearTranscript: () => void;
}

export function useWebSocketRecording(config: WebSocketRecordingConfig): UseWebSocketRecordingResult {
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptData, setTranscriptData] = useState<TranscriptData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPaused, setIsPaused] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);

  const { websocketUrl, sampleRate = 16000, bufferSize = 4096, onTranscriptionUpdate, onError, onConnectionChange } = config;

  // Initialize default speakers
  const defaultSpeakers: Speaker[] = [
    { id: "officer", name: "Officer", type: "officer", color: "#2563eb" },
    { id: "witness", name: "Witness", type: "witness", color: "#dc3545" },
  ];

  // Initialize WebSocket connection
  const connectWebSocket = useCallback(() => {
    try {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        return;
      }

      wsRef.current = new WebSocket(websocketUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        onConnectionChange?.(true);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('----------', data)
          // Handle different message types from the server
          if (data.type === 'transcription') {
            const newSegment: TranscriptSegment = {
              speaker: data.speaker || 'witness',
              timestamp: data.timestamp || formatTimestamp(Date.now()),
              text: data.text || '',
            };

            setTranscriptData(prev => {
              const updated = prev ? {
                ...prev,
                segments: [...prev.segments, newSegment]
              } : {
                speakers: defaultSpeakers,
                segments: [newSegment]
              };
              
              onTranscriptionUpdate?.(updated);
              return updated;
            });
          } else if (data.type === 'partial_transcription') {
            // Handle partial/interim transcription results
            setTranscriptData(prev => {
              if (!prev) return null;
              
              const segments = [...prev.segments];
              const lastSegment = segments[segments.length - 1];
              
              if (lastSegment && data.is_partial) {
                // Update the last segment with partial text
                segments[segments.length - 1] = {
                  ...lastSegment,
                  text: data.text || lastSegment.text
                };
              }
              
              const updated = { ...prev, segments };
              onTranscriptionUpdate?.(updated);
              return updated;
            });
          } else if (data.type === 'error') {
            const errorMsg = data.message || 'Transcription error occurred';
            setError(errorMsg);
            onError?.(errorMsg);
          }
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
          setError('Failed to parse transcription data');
        }
      };

      wsRef.current.onerror = (event) => {
        console.error('WebSocket error:', event);
        setError('WebSocket connection error');
        onError?.('WebSocket connection error');
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        onConnectionChange?.(false);
      };

    } catch (err) {
      console.error('Failed to connect WebSocket:', err);
      setError('Failed to connect to transcription service');
      onError?.('Failed to connect to transcription service');
    }
  }, [websocketUrl, onTranscriptionUpdate, onError, onConnectionChange]);

  // Format timestamp helper
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  // Initialize audio recording
  const initializeAudio = async (): Promise<MediaStream> => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      streamRef.current = stream;
      return stream;
    } catch (err) {
      throw new Error('Failed to access microphone');
    }
  };

  // Send audio data to WebSocket
  const sendAudioData = (audioData: Float32Array) => {
    if (wsRef.current?.readyState === WebSocket.OPEN && !isPaused) {
      // Convert Float32Array to Int16Array for better compression
      const int16Data = new Int16Array(audioData.length);
      for (let i = 0; i < audioData.length; i++) {
        int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768));
      }
      
      // Send as binary data
      wsRef.current.send(int16Data.buffer);
    }
  };

  // Start recording
  const startRecording = async (): Promise<void> => {
    try {
      setError(null);
      
      // Connect WebSocket if not connected
      if (!isConnected) {
        connectWebSocket();
        // Wait a bit for connection
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Initialize audio
      const stream = await initializeAudio();
      
      // Create audio context for processing
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: sampleRate
      });

      const source = audioContextRef.current.createMediaStreamSource(stream);
      processorRef.current = audioContextRef.current.createScriptProcessor(bufferSize, 1, 1);

      processorRef.current.onaudioprocess = (event) => {
        const inputData = event.inputBuffer.getChannelData(0);
        sendAudioData(inputData);
      };

      source.connect(processorRef.current);
      processorRef.current.connect(audioContextRef.current.destination);

      setIsRecording(true);
      setIsPaused(false);

      // Initialize transcript data if not exists
      if (!transcriptData) {
        setTranscriptData({
          speakers: defaultSpeakers,
          segments: []
        });
      }

    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to start recording';
      setError(errorMsg);
      onError?.(errorMsg);
      throw err;
    }
  };

  // Stop recording
  const stopRecording = () => {
    setIsRecording(false);
    setIsPaused(false);

    // Stop audio processing
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Close WebSocket
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  // Pause recording
  const pauseRecording = () => {
    setIsPaused(true);
  };

  // Resume recording
  const resumeRecording = () => {
    setIsPaused(false);
  };

  // Clear transcript
  const clearTranscript = () => {
    setTranscriptData({
      speakers: defaultSpeakers,
      segments: []
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopRecording();
    };
  }, []);

  return {
    isConnected,
    isRecording,
    transcriptData,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearTranscript,
  };
}
