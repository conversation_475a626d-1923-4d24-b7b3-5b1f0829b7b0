import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse, validateRequiredFields } from '@/lib/api-auth';
import { CaseService } from '@/lib/supabase';
import type { AuthenticatedRequest } from '@/lib/api-auth';

// GET /api/cases - Get all cases (with filtering)
export const GET = withAuth(
  async (request: AuthenticatedRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      const filters = {
        status: searchParams.get('status') || undefined,
        officer: searchParams.get('officer') || undefined,
        limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
        offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
      };

      // If user is not admin/supervisor, filter to their own cases
      if (request.userRole === 'officer') {
        filters.officer = request.userId;
      }

      const cases = await CaseService.getCases(filters);

      return createSuccessResponse({
        cases,
        total: cases.length,
        filters,
      });

    } catch (error) {
      console.error('Error fetching cases:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to fetch cases',
        500
      );
    }
  },
  { requiredPermissions: ['cases:read:own'] }
);

// POST /api/cases - Create new case
export const POST = withAuth(
  async (request: AuthenticatedRequest) => {
    try {
      const body = await request.json();

      // Validate required fields
      const missingFields = validateRequiredFields(body, [
        'incident_location',
        'incident_date',
        'incident_time',
        'assigned_officer_id',
      ]);

      if (missingFields.length > 0) {
        return createErrorResponse(
          'Validation Error',
          `Missing required fields: ${missingFields.join(', ')}`
        );
      }

      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(body.incident_date)) {
        return createErrorResponse(
          'Validation Error',
          'incident_date must be in YYYY-MM-DD format'
        );
      }

      // Validate time format
      const timeRegex = /^\d{2}:\d{2}$/;
      if (!timeRegex.test(body.incident_time)) {
        return createErrorResponse(
          'Validation Error',
          'incident_time must be in HH:MM format'
        );
      }

      const newCase = await CaseService.createCase(body);

      return createSuccessResponse(newCase, 201);

    } catch (error) {
      console.error('Error creating case:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to create case',
        500
      );
    }
  },
  { requiredPermissions: ['cases:create'] }
);
