import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse, validateRequiredFields } from '@/lib/api-auth';
import { InterviewService, CaseService } from '@/lib/supabase';
import type { AuthenticatedRequest } from '@/lib/api-auth';

// GET /api/cases/{caseId}/interviews - Get interviews for a case
export const GET = withAuth(
  async (request: AuthenticatedRequest, { params }: { params: { caseId: string } }) => {
    try {
      const { caseId } = params;
      const { searchParams } = new URL(request.url);
      
      // Verify case exists and user has access
      const caseData = await CaseService.getCaseById(caseId);
      if (!caseData) {
        return createErrorResponse(
          'Not Found',
          'Case not found',
          404
        );
      }

      const filters = {
        caseId,
        status: searchParams.get('status') || undefined,
        limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
        offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
      };

      const interviews = await InterviewService.getInterviews(filters);

      return createSuccessResponse({
        interviews,
        total: interviews.length,
        caseId,
        filters,
      });

    } catch (error) {
      console.error('Error fetching interviews:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to fetch interviews',
        500
      );
    }
  },
  { requiredPermissions: ['interviews:read:own'] }
);

// POST /api/cases/{caseId}/interviews - Create new interview
export const POST = withAuth(
  async (request: AuthenticatedRequest, { params }: { params: { caseId: string } }) => {
    try {
      const { caseId } = params;
      const body = await request.json();

      // Verify case exists and user has access
      const caseData = await CaseService.getCaseById(caseId);
      if (!caseData) {
        return createErrorResponse(
          'Not Found',
          'Case not found',
          404
        );
      }

      // Validate required fields
      const missingFields = validateRequiredFields(body, [
        'witness.name',
        'witness.type',
        'witness.contact',
      ]);

      if (missingFields.length > 0) {
        return createErrorResponse(
          'Validation Error',
          `Missing required fields: ${missingFields.join(', ')}`
        );
      }

      // Validate witness type
      const validWitnessTypes = ['Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder'];
      if (!validWitnessTypes.includes(body.witness.type)) {
        return createErrorResponse(
          'Validation Error',
          `Invalid witness type. Must be one of: ${validWitnessTypes.join(', ')}`
        );
      }

      // Validate interview environment if provided
      if (body.witness.environment) {
        const validEnvironments = ['controlled', 'field'];
        if (!validEnvironments.includes(body.witness.environment)) {
          return createErrorResponse(
            'Validation Error',
            `Invalid interview environment. Must be one of: ${validEnvironments.join(', ')}`
          );
        }
      }

      // Add interviewing officer ID from authenticated user
      const interviewData = {
        ...body,
        interviewing_officer_id: request.userId,
      };

      const newInterview = await InterviewService.createInterview(caseId, interviewData);

      return createSuccessResponse(newInterview, 201);

    } catch (error) {
      console.error('Error creating interview:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to create interview',
        500
      );
    }
  },
  { requiredPermissions: ['interviews:create'] }
);
