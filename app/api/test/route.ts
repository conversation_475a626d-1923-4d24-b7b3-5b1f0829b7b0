import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse } from '@/lib/api-auth';
import { CaseService, UserService } from '@/lib/supabase';
import type { AuthenticatedRequest } from '@/lib/api-auth';

// Test endpoint to verify authentication and database integration
export const GET = withAuth(
  async (request: AuthenticatedRequest) => {
    try {
      // Test user data
      const currentUser = request.user;
      
      // Test database connection by fetching users
      const users = await UserService.getUsers();
      
      // Test case service
      const cases = await CaseService.getCases({ limit: 5 });

      return createSuccessResponse({
        message: 'Integration test successful!',
        currentUser: {
          id: currentUser?.id,
          email: currentUser?.email,
          fullName: currentUser?.fullName,
          role: currentUser?.role,
        },
        databaseStats: {
          totalUsers: users.length,
          totalCases: cases.length,
        },
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('Test endpoint error:', error);
      return createErrorResponse(
        'Test Failed',
        error instanceof Error ? error.message : 'Unknown error occurred',
        500
      );
    }
  }
);

// Public test endpoint (no auth required)
export async function POST() {
  try {
    return createSuccessResponse({
      message: 'Public test endpoint working!',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
    });
  } catch (error) {
    return createErrorResponse(
      'Public Test Failed',
      error instanceof Error ? error.message : 'Unknown error occurred',
      500
    );
  }
}
