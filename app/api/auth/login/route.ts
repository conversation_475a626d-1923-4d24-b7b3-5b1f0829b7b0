import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  validateRequiredFields, 
  validateEmail,
  checkRateLimit 
} from '@/lib/api-auth';
import { UserService } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || 'unknown';
    const rateLimit = checkRateLimit(`login:${clientIP}`, 5, 15 * 60 * 1000); // 5 attempts per 15 minutes
    
    if (!rateLimit.allowed) {
      return createErrorResponse(
        'Too Many Requests',
        'Too many login attempts. Please try again later.',
        429
      );
    }

    const body = await request.json();
    
    // Validate required fields
    const missingFields = validateRequiredFields(body, ['email', 'password']);
    if (missingFields.length > 0) {
      return createErrorResponse(
        'Validation Error',
        `Missing required fields: ${missingFields.join(', ')}`
      );
    }

    const { email, password } = body;

    // Validate email format
    if (!validateEmail(email)) {
      return createErrorResponse(
        'Validation Error',
        'Invalid email format'
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Attempt to sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return createErrorResponse(
        'Authentication Failed',
        error.message,
        401
      );
    }

    if (!data.user) {
      return createErrorResponse(
        'Authentication Failed',
        'Invalid credentials',
        401
      );
    }

    // Get user profile
    const userProfile = await UserService.getUserByEmail(email);
    
    if (!userProfile) {
      return createErrorResponse(
        'User Not Found',
        'User profile not found in system',
        404
      );
    }

    if (!userProfile.isActive) {
      return createErrorResponse(
        'Account Disabled',
        'Your account has been disabled. Please contact an administrator.',
        403
      );
    }

    return createSuccessResponse({
      user: {
        id: data.user.id,
        email: data.user.email,
        profile: userProfile,
      },
      session: data.session,
    });

  } catch (error) {
    console.error('Login error:', error);
    return createErrorResponse(
      'Internal Server Error',
      'An unexpected error occurred during login',
      500
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
