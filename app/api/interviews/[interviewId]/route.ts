import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse, createErrorResponse, validateRequiredFields } from '@/lib/api-auth';
import { InterviewService } from '@/lib/supabase';
import type { AuthenticatedRequest } from '@/lib/api-auth';

// GET /api/interviews/{interviewId} - Get interview by ID
export const GET = withAuth(
  async (request: AuthenticatedRequest, { params }: { params: { interviewId: string } }) => {
    try {
      const { interviewId } = params;

      const interview = await InterviewService.getInterviewById(interviewId);
      
      if (!interview) {
        return createErrorResponse(
          'Not Found',
          'Interview not found',
          404
        );
      }

      return createSuccessResponse(interview);

    } catch (error) {
      console.error('Error fetching interview:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to fetch interview',
        500
      );
    }
  },
  { requiredPermissions: ['interviews:read:own'] }
);

// PUT /api/interviews/{interviewId} - Update interview
export const PUT = withAuth(
  async (request: AuthenticatedRequest, { params }: { params: { interviewId: string } }) => {
    try {
      const { interviewId } = params;
      const body = await request.json();

      // Verify interview exists and user has access
      const existingInterview = await InterviewService.getInterviewById(interviewId);
      if (!existingInterview) {
        return createErrorResponse(
          'Not Found',
          'Interview not found',
          404
        );
      }

      // Validate witness type if provided
      if (body.witness?.type) {
        const validWitnessTypes = ['Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder'];
        if (!validWitnessTypes.includes(body.witness.type)) {
          return createErrorResponse(
            'Validation Error',
            `Invalid witness type. Must be one of: ${validWitnessTypes.join(', ')}`
          );
        }
      }

      // Validate interview environment if provided
      if (body.witness?.environment) {
        const validEnvironments = ['controlled', 'field'];
        if (!validEnvironments.includes(body.witness.environment)) {
          return createErrorResponse(
            'Validation Error',
            `Invalid interview environment. Must be one of: ${validEnvironments.join(', ')}`
          );
        }
      }

      // Validate status if provided
      if (body.status) {
        const validStatuses = ['scheduled', 'in_progress', 'completed', 'cancelled'];
        if (!validStatuses.includes(body.status)) {
          return createErrorResponse(
            'Validation Error',
            `Invalid status. Must be one of: ${validStatuses.join(', ')}`
          );
        }
      }

      const updatedInterview = await InterviewService.updateInterview(interviewId, body);

      return createSuccessResponse(updatedInterview);

    } catch (error) {
      console.error('Error updating interview:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to update interview',
        500
      );
    }
  },
  { requiredPermissions: ['interviews:update:own'] }
);

// DELETE /api/interviews/{interviewId} - Delete interview
export const DELETE = withAuth(
  async (request: AuthenticatedRequest, { params }: { params: { interviewId: string } }) => {
    try {
      const { interviewId } = params;

      // Verify interview exists and user has access
      const existingInterview = await InterviewService.getInterviewById(interviewId);
      if (!existingInterview) {
        return createErrorResponse(
          'Not Found',
          'Interview not found',
          404
        );
      }

      await InterviewService.deleteInterview(interviewId);

      return createSuccessResponse({ message: 'Interview deleted successfully' });

    } catch (error) {
      console.error('Error deleting interview:', error);
      return createErrorResponse(
        'Internal Server Error',
        'Failed to delete interview',
        500
      );
    }
  },
  { requiredPermissions: ['interviews:delete:own'] }
);
