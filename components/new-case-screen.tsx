"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Loader2 } from "lucide-react"
import { useCases } from "@/hooks/use-cases"
import { useAuth } from "@/contexts/AuthContext"
import type { Screen, AppState } from "@/app/page"
import type { Case } from "@/types/database"

interface NewCaseScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function NewCaseScreen({ onNavigate, appState, updateAppState }: NewCaseScreenProps) {
  const { user } = useAuth()
  const { createCase } = useCases()
  const [formData, setFormData] = useState({
    incidentLocation: "",
    incidentDate: "",
    incidentTime: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setError("No authenticated user found")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const newCase = await createCase({
        incident_location: formData.incidentLocation,
        incident_date: formData.incidentDate,
        incident_time: formData.incidentTime,
        assigned_officer_id: user.id,
      })

      updateAppState({ currentCase: newCase })
      onNavigate("witness-setup")
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create case')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-selection")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">New Case Setup</h2>
        <div></div>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">

            <div className="space-y-2">
              <Label htmlFor="incident-location">Incident Location</Label>
              <Input
                id="incident-location"
                value={formData.incidentLocation}
                onChange={(e) => handleInputChange("incidentLocation", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident-date">Incident Date</Label>
              <Input
                id="incident-date"
                type="date"
                value={formData.incidentDate}
                onChange={(e) => handleInputChange("incidentDate", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident-time">Incident Time</Label>
              <Input
                id="incident-time"
                type="time"
                value={formData.incidentTime}
                onChange={(e) => handleInputChange("incidentTime", e.target.value)}
                required
              />
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating Case...
                </>
              ) : (
                'Create Case'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
