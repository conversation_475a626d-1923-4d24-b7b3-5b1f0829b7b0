'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { AuthService, UserService } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export function AuthDebug() {
  const { user, loading, isAuthenticated } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [checking, setChecking] = useState(false)

  const checkAuthState = async () => {
    setChecking(true)
    try {
      // Check Supabase auth session
      const session = await AuthService.getSession()
      const currentUser = await AuthService.getCurrentUser()

      // Check if user exists in our users table
      let userProfile = null
      if (currentUser?.email) {
        userProfile = await UserService.getUserByEmail(currentUser.email)
      }

      // Also specifically <NAME_EMAIL>
      const clarkUser = await UserService.getUserByEmail('<EMAIL>')

      // Get all users to see what's in the table
      const allUsers = await UserService.getUsers()

      setDebugInfo({
        session: session ? {
          user_id: session.user?.id,
          email: session.user?.email,
          expires_at: session.expires_at
        } : null,
        currentUser: currentUser ? {
          id: currentUser.id,
          email: currentUser.email,
          user_metadata: currentUser.user_metadata
        } : null,
        userProfile,
        clarkUser,
        allUsersCount: allUsers.length,
        allUsers: allUsers.map(u => ({ email: u.email, role: u.role, isActive: u.isActive })),
        contextUser: user,
        isAuthenticated,
        loading
      })
    } catch (error) {
      setDebugInfo({
        error: error instanceof Error ? error.message : 'Unknown error',
        contextUser: user,
        isAuthenticated,
        loading
      })
    } finally {
      setChecking(false)
    }
  }

  useEffect(() => {
    checkAuthState()
  }, [user, isAuthenticated])

  return (
    <Card className="w-full max-w-4xl mx-auto mt-4">
      <CardHeader>
        <CardTitle>Authentication Debug Information</CardTitle>
        <CardDescription>
          Debug information to help diagnose authentication issues
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button onClick={checkAuthState} disabled={checking}>
            {checking ? 'Checking...' : 'Refresh Auth State'}
          </Button>
          
          {debugInfo && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">Auth Context State</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify({
                    user: debugInfo.contextUser,
                    isAuthenticated: debugInfo.isAuthenticated,
                    loading: debugInfo.loading
                  }, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold text-lg">Supabase Auth Session</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.session, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold text-lg">Current Supabase User</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.currentUser, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold text-lg">User Profile from Database</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.userProfile, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold text-lg">Clark User Check</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.clarkUser, null, 2)}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold text-lg">All Users in Database ({debugInfo.allUsersCount})</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.allUsers, null, 2)}
                </pre>
              </div>

              {debugInfo.error && (
                <div>
                  <h3 className="font-semibold text-lg text-red-600">Error</h3>
                  <pre className="bg-red-100 p-3 rounded text-sm overflow-auto">
                    {debugInfo.error}
                  </pre>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
