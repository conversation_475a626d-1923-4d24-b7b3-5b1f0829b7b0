"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Settings, TestTube } from "lucide-react"

interface WebSocketConfigProps {
  onEndpointChange: (endpoint: string) => void
  currentEndpoint: string
}

export function WebSocketConfig({ onEndpointChange, currentEndpoint }: WebSocketConfigProps) {
  const [endpoint, setEndpoint] = useState(currentEndpoint)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)

  const handleSave = () => {
    onEndpointChange(endpoint)
    setTestResult(null)
  }

  const testConnection = async () => {
    setIsTestingConnection(true)
    setTestResult(null)

    try {
      const ws = new WebSocket(endpoint)
      
      const timeout = setTimeout(() => {
        ws.close()
        setTestResult({
          success: false,
          message: 'Connection timeout - server did not respond within 5 seconds'
        })
        setIsTestingConnection(false)
      }, 5000)

      ws.onopen = () => {
        clearTimeout(timeout)
        setTestResult({
          success: true,
          message: 'Successfully connected to WebSocket endpoint'
        })
        ws.close()
        setIsTestingConnection(false)
      }

      ws.onerror = () => {
        clearTimeout(timeout)
        setTestResult({
          success: false,
          message: 'Failed to connect - check if the endpoint is correct and server is running'
        })
        setIsTestingConnection(false)
      }

      ws.onclose = (event) => {
        if (event.code !== 1000) { // 1000 is normal closure
          clearTimeout(timeout)
          setTestResult({
            success: false,
            message: `Connection closed unexpectedly (code: ${event.code})`
          })
          setIsTestingConnection(false)
        }
      }

    } catch (error) {
      setTestResult({
        success: false,
        message: `Invalid WebSocket URL: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      setIsTestingConnection(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          WebSocket Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="websocket-endpoint">WebSocket Endpoint URL</Label>
          <Input
            id="websocket-endpoint"
            type="url"
            placeholder="ws://localhost:8080/transcribe"
            value={endpoint}
            onChange={(e) => setEndpoint(e.target.value)}
          />
          <p className="text-sm text-muted-foreground">
            Enter the WebSocket endpoint URL for your transcription service
          </p>
        </div>

        {testResult && (
          <Alert className={testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
            <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
              {testResult.message}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          <Button 
            onClick={testConnection} 
            disabled={isTestingConnection || !endpoint}
            variant="outline"
            className="flex items-center gap-2"
          >
            <TestTube className="w-4 h-4" />
            {isTestingConnection ? 'Testing...' : 'Test Connection'}
          </Button>
          
          <Button 
            onClick={handleSave}
            disabled={!endpoint || endpoint === currentEndpoint}
          >
            Save Configuration
          </Button>
        </div>

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Expected WebSocket Protocol:</h4>
          <div className="text-sm space-y-2">
            <p><strong>Audio Input:</strong> Binary audio data (Int16Array buffer)</p>
            <p><strong>Expected Response Format:</strong></p>
            <pre className="bg-background p-2 rounded text-xs overflow-x-auto">
{`{
  "type": "transcription",
  "speaker": "witness" | "officer",
  "text": "transcribed text",
  "timestamp": "MM:SS"
}

// For partial results:
{
  "type": "partial_transcription", 
  "text": "partial text...",
  "is_partial": true
}

// For errors:
{
  "type": "error",
  "message": "error description"
}`}
            </pre>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
