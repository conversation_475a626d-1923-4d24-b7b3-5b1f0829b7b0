"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { TestTube } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { UserMenu } from "@/components/auth/UserMenu"
import type { Screen } from "@/app/page"

interface StartScreenProps {
  onNavigate: (screen: Screen) => void
}

export function StartScreen({ onNavigate }: StartScreenProps) {
  const { user } = useAuth()

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div className="text-center flex-1">
          <h1 className="text-4xl font-bold text-primary mb-2">Fire Investigation Unit</h1>
          <p className="text-xl text-muted-foreground">Interview System</p>
        </div>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold mb-4">Officer Authentication</h3>
            <div className="bg-secondary p-4 rounded-lg space-y-2">
              <p className="font-medium">{user?.fullName || 'Unknown Officer'}</p>
              <p className="text-sm text-muted-foreground">Badge: {user?.badgeNumber || 'N/A'}</p>
              <p className="text-sm text-muted-foreground">{user?.department || 'Fire Investigation Unit'}</p>
              <p className="text-sm text-muted-foreground">Role: {user?.role || 'officer'}</p>
            </div>
          </div>

          <div className="space-y-3">
            <Button className="w-full" size="lg" onClick={() => onNavigate("case-selection")}>
              Start New Interview
            </Button>
            <Button variant="secondary" className="w-full" onClick={() => onNavigate("case-history")}>
              View Case History
            </Button>
            {/* {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => window.open('/test', '_blank')}
              >
                <TestTube className="w-4 h-4 mr-2" />
                Integration Test
              </Button>
            )} */}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
