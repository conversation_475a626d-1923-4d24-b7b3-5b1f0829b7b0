# FIU Witness Interview Database Setup

This document provides comprehensive instructions for setting up the PostgreSQL database on Supabase for the Fire Investigation Unit witness interview system.

## Prerequisites

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **Node.js Project**: Ensure you have a Next.js project set up
3. **Environment Variables**: Prepare your environment configuration

## Quick Setup

### 1. Create Supabase Project

1. Log into your Supabase dashboard
2. Click "New Project"
3. Choose your organization
4. Enter project name: `fiu-witness-interview`
5. Enter a secure database password
6. Select your region
7. Click "Create new project"

### 2. Initialize Database Schema

1. Navigate to the SQL Editor in your Supabase dashboard
2. Copy the contents of `init-database.sql`
3. Paste into the SQL Editor
4. Click "Run" to execute the script

### 3. Configure Environment Variables

Create a `.env.local` file in your project root:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. Install Dependencies

```bash
npm install @supabase/supabase-js
# or
yarn add @supabase/supabase-js
```

### 5. Set Up Storage Buckets

In the Supabase dashboard, go to Storage and create two buckets:

1. **audio-recordings** (Private)
   - For storing interview audio files
   - Enable RLS policies

2. **exported-documents** (Private)
   - For storing generated PDF/Word documents
   - Enable RLS policies

## Database Schema Overview

### Core Tables

1. **cases** - Fire investigation cases
2. **interviews** - Witness interviews
3. **transcription_speakers** - Speaker identification
4. **transcription_segments** - Transcription text segments
5. **statements** - Formal interview statements
6. **audio_recordings** - Audio file metadata
7. **export_logs** - Document export tracking

### Key Features

- **Row Level Security (RLS)** enabled on all tables
- **Automatic timestamps** with triggers
- **Data validation** with check constraints
- **Foreign key relationships** for data integrity
- **Indexes** for optimal query performance

## Usage Examples

### Basic Operations

```typescript
import { CaseService, InterviewService } from '@/lib/supabase';

// Create a new case
const newCase = await CaseService.createCase({
  incident_location: "123 Main Street, Apartment 4B",
  incident_date: "2025-06-20",
  incident_time: "14:30",
  officer: "<EMAIL>"
});

// Get all cases
const cases = await CaseService.getCases();

// Create an interview
const interview = await InterviewService.createInterview(newCase.id, {
  witness: {
    name: "Sarah Williams",
    type: "Neighbor",
    contact: "555-0123",
    environment: "field"
  }
});
```

### Real-time Transcription

```typescript
import { TranscriptionService } from '@/lib/supabase';

// Save transcription data
await TranscriptionService.saveTranscriptionData(interviewId, {
  speakers: [
    { id: "S1", name: "Officer Johnson", color: "#2563eb" },
    { id: "S2", name: "Sarah Williams", color: "#dc3545" }
  ],
  segments: [
    {
      speaker: "S1",
      timestamp: "00:00",
      text: "Please state your name for the record.",
      confidence: 0.95
    }
  ]
});
```

### Statement Management

```typescript
import { StatementService } from '@/lib/supabase';

// Create a statement
const statement = await StatementService.createStatement(
  interviewId,
  "Witness observed smoke coming from kitchen window at approximately 2:30 PM...",
  "Witness appeared credible and provided detailed timeline."
);

// Update statement
await StatementService.updateStatement(interviewId, {
  content: "Updated statement content...",
  officer_notes: "Additional notes..."
});
```

## Security Configuration

### Row Level Security Policies

The database includes comprehensive RLS policies:

- **Officers** can only access cases assigned to them
- **Admin users** have full access to all data
- **Transcription data** inherits access from interview permissions
- **Audio files** are protected by storage policies

### Authentication Integration

The RLS policies expect JWT tokens with:
- `email` claim for officer identification
- `role` claim for admin access

Example JWT payload:
```json
{
  "email": "<EMAIL>",
  "role": "officer",
  "aud": "authenticated",
  "exp": 1640995200
}
```

## Storage Configuration

### Audio Recordings Bucket

```sql
-- Storage policy for audio recordings
CREATE POLICY "Officers can upload audio for their interviews" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'audio-recordings' AND
  EXISTS (
    SELECT 1 FROM interviews i
    JOIN cases c ON c.id = i.case_id
    WHERE i.id::text = (storage.foldername(name))[1]
    AND c.officer = auth.jwt() ->> 'email'
  )
);
```

### Exported Documents Bucket

```sql
-- Storage policy for exported documents
CREATE POLICY "Officers can access exports for their interviews" ON storage.objects
FOR SELECT USING (
  bucket_id = 'exported-documents' AND
  EXISTS (
    SELECT 1 FROM interviews i
    JOIN cases c ON c.id = i.case_id
    WHERE i.id::text = (storage.foldername(name))[1]
    AND c.officer = auth.jwt() ->> 'email'
  )
);
```

## Performance Optimization

### Indexes

The schema includes optimized indexes for:
- Case filtering by status and officer
- Interview queries by case and status
- Transcription segment ordering
- Export log chronological access

### Query Optimization Tips

1. **Use specific filters** when querying cases and interviews
2. **Limit result sets** with pagination
3. **Leverage indexes** by filtering on indexed columns
4. **Use real-time subscriptions** for live updates

## Backup and Maintenance

### Automated Backups

Supabase provides automatic daily backups. For additional protection:

1. Enable Point-in-Time Recovery (PITR)
2. Set up custom backup schedules
3. Export critical data regularly

### Data Retention

Consider implementing data retention policies:

```sql
-- Example: Delete export logs older than 1 year
DELETE FROM export_logs 
WHERE created_at < NOW() - INTERVAL '1 year';

-- Example: Archive completed cases older than 5 years
UPDATE cases 
SET status = 'Archived' 
WHERE status = 'Completed' 
AND created_at < NOW() - INTERVAL '5 years';
```

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**
   - Verify JWT token contains correct claims
   - Check policy conditions match your auth setup

2. **Foreign Key Violations**
   - Ensure parent records exist before creating children
   - Use transactions for related operations

3. **Storage Upload Failures**
   - Verify bucket policies are correctly configured
   - Check file size limits and formats

### Debug Queries

```sql
-- Check RLS policy evaluation
SELECT * FROM cases WHERE id = 'FIU-2025-001';

-- Verify transcription data integrity
SELECT 
  i.id,
  COUNT(ts.id) as speaker_count,
  COUNT(tg.id) as segment_count
FROM interviews i
LEFT JOIN transcription_speakers ts ON ts.interview_id = i.id
LEFT JOIN transcription_segments tg ON tg.interview_id = i.id
GROUP BY i.id;
```

## Migration and Updates

### Schema Updates

When updating the schema:

1. Create migration scripts
2. Test in staging environment
3. Use transactions for complex changes
4. Update TypeScript types accordingly

### Version Control

Keep track of schema changes:
- Version your migration scripts
- Document breaking changes
- Update API documentation

## Support

For issues related to:
- **Database schema**: Check the `database-schema.md` file
- **API integration**: Refer to the `api.yml` specification
- **Frontend models**: See `types/database.ts`
- **Supabase configuration**: Visit [Supabase documentation](https://supabase.com/docs)
