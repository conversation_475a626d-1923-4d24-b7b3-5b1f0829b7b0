// Script to test and understand RLS policies
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://valqgfpmtmmqfzwhjowz.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZhbHFnZnBtdG1tcWZ6d2hqb3d6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MzQ0MjQsImV4cCI6MjA2NjMxMDQyNH0.sB254MqNtFVE2PRIgopZrBy-8fMABfnMHjGk74LaFT8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testRLSPolicies() {
  console.log('🔒 Testing Row Level Security Policies\n');
  
  try {
    // Test 1: Check if we can read from users table
    console.log('1. Testing read access to users table...');
    const { data: users, error: readError } = await supabase
      .from('users')
      .select('*');

    if (readError) {
      console.log('❌ Read access denied:', readError.message);
    } else {
      console.log('✅ Read access granted, found', users.length, 'users');
    }

    // Test 2: Check if we can write to users table
    console.log('\n2. Testing write access to users table...');
    const { data: newUser, error: writeError } = await supabase
      .from('users')
      .insert([{
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'officer',
        is_active: true
      }])
      .select();

    if (writeError) {
      console.log('❌ Write access denied:', writeError.message);
      console.log('   Error code:', writeError.code);
    } else {
      console.log('✅ Write access granted, created user:', newUser[0].email);
      
      // Clean up test user
      await supabase
        .from('users')
        .delete()
        .eq('email', '<EMAIL>');
    }

    // Test 3: Check current auth status
    console.log('\n3. Checking current authentication status...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.log('❌ Auth error:', authError.message);
    } else if (user) {
      console.log('✅ Authenticated as:', user.email);
      console.log('   User ID:', user.id);
      console.log('   User metadata:', user.user_metadata);
    } else {
      console.log('📭 Not authenticated (anonymous user)');
    }

    // Test 4: Try to authenticate with a test user
    console.log('\n4. Testing authentication with existing user...');
    const { data: authData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'test123' // This will likely fail, but let's see the error
    });

    if (signInError) {
      console.log('❌ Sign in failed:', signInError.message);
    } else {
      console.log('✅ Sign in successful for:', authData.user?.email);
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }

  console.log('\n📋 Summary:');
  console.log('The issue is that Row Level Security (RLS) is enabled on the users table,');
  console.log('but there are no policies that allow anonymous users to read or write.');
  console.log('\nTo fix this, you need to either:');
  console.log('1. Create RLS policies that allow the operations you need');
  console.log('2. Temporarily disable RLS to create initial users');
  console.log('3. Use the Supabase dashboard to manually create users');
  console.log('4. Use a service role key with elevated permissions');
}

testRLSPolicies();
