# Required Dependencies for Authentication

## Install Authentication Dependencies

Run the following command to install the required dependencies for Supabase authentication:

```bash
npm install @supabase/auth-helpers-nextjs @supabase/supabase-js
```

Or with yarn:

```bash
yarn add @supabase/auth-helpers-nextjs @supabase/supabase-js
```

## Updated package.json Dependencies

Add these to your `package.json` dependencies:

```json
{
  "dependencies": {
    "@supabase/auth-helpers-nextjs": "^0.8.7",
    "@supabase/supabase-js": "^2.38.4"
  }
}
```

## Environment Variables

Create or update your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional: For development
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## TypeScript Configuration

Ensure your `tsconfig.json` includes the necessary path mappings:

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["components/*"],
      "@/lib/*": ["lib/*"],
      "@/contexts/*": ["contexts/*"],
      "@/types/*": ["types/*"]
    }
  }
}
```

## Next.js Configuration

Update your `next.config.js` if needed:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverActions: true,
  },
  // Add any other configuration you need
}

module.exports = nextConfig
```

## Installation Steps

1. **Install dependencies**:
   ```bash
   npm install @supabase/auth-helpers-nextjs @supabase/supabase-js
   ```

2. **Set up environment variables**:
   - Copy your Supabase URL and keys from the Supabase dashboard
   - Add them to `.env.local`

3. **Run the database initialization**:
   - Execute the `init-database.sql` script in your Supabase SQL editor

4. **Start your development server**:
   ```bash
   npm run dev
   ```

## Verification

After installation, verify everything is working:

1. **Check Supabase connection**:
   ```typescript
   import { supabase } from '@/lib/supabase';
   
   // Test connection
   const { data, error } = await supabase.from('users').select('count');
   console.log('Supabase connected:', !error);
   ```

2. **Test authentication**:
   - Navigate to `/login`
   - Try logging in with test credentials
   - Check browser console for any errors

3. **Verify middleware**:
   - Try accessing protected routes without authentication
   - Should redirect to login page

## Common Issues

### 1. Module Not Found Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### 2. TypeScript Errors
```bash
# Regenerate TypeScript declarations
npm run build
```

### 3. Environment Variable Issues
- Ensure `.env.local` is in your project root
- Restart your development server after adding variables
- Check that variables start with `NEXT_PUBLIC_` for client-side access

### 4. Supabase Connection Issues
- Verify your Supabase project URL and keys
- Check your project's API settings in Supabase dashboard
- Ensure RLS policies are properly configured

## Development Workflow

1. **Start development server**:
   ```bash
   npm run dev
   ```

2. **Access the application**:
   - Open http://localhost:3000
   - Should redirect to login if not authenticated

3. **Create test users**:
   - Use the SQL script to create test users
   - Or use the signup API endpoint

4. **Test authentication flow**:
   - Login with different user roles
   - Verify role-based access control
   - Test protected routes and API endpoints

This setup provides a complete authentication system with Supabase integration for your Next.js application.
