<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIU Interview System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="screen hidden">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <h3>Processing Statement...</h3>
                <p>Please wait while we transcribe and generate your statement.</p>
            </div>
        </div>

        <!-- Start Screen -->
        <div id="start-screen" class="screen active">
            <div class="header">
                <h1>Fire Investigation Unit</h1>
                <p class="subtitle">Interview System</p>
            </div>
            <div class="card auth-card">
                <div class="card__body">
                    <div class="officer-info">
                        <h3>Officer Authentication</h3>
                        <div class="officer-details">
                            <p><strong>Detective <PERSON></strong></p>
                            <p>Badge: FIU-2847</p>
                            <p>Fire Investigation Unit</p>
                            <p>Central Division</p>
                        </div>
                    </div>
                    <button class="btn btn--primary btn--full-width btn--lg" onclick="showScreen('case-selection')">
                        Start New Interview
                    </button>
                    <button class="btn btn--secondary btn--full-width" onclick="showScreen('case-history')">
                        View Case History
                    </button>
                </div>
            </div>
        </div>

        <!-- Case Selection Screen -->
        <div id="case-selection" class="screen">
            <div class="header">
                <button class="btn btn--outline btn--sm back-btn" onclick="showScreen('start-screen')">← Back</button>
                <h2>Select Case</h2>
            </div>
            <div class="case-list">
                <div class="card case-card" onclick="selectCase('FIU-2025-001')">
                    <div class="card__body">
                        <div class="case-header">
                            <h4>FIU-2025-001</h4>
                            <span class="status status--warning">In Progress</span>
                        </div>
                        <p><strong>Location:</strong> 123 Main Street, Apartment 4B</p>
                        <p><strong>Date:</strong> June 20, 2025 at 2:30 PM</p>
                        <p><strong>Officer:</strong> Detective Johnson</p>
                    </div>
                </div>
                <div class="card case-card" onclick="selectCase('FIU-2025-002')">
                    <div class="card__body">
                        <div class="case-header">
                            <h4>FIU-2025-002</h4>
                            <span class="status status--success">Completed</span>
                        </div>
                        <p><strong>Location:</strong> 456 Oak Avenue, Single Family Home</p>
                        <p><strong>Date:</strong> June 18, 2025 at 9:15 AM</p>
                        <p><strong>Officer:</strong> Detective Smith</p>
                    </div>
                </div>
            </div>
            <button class="btn btn--secondary btn--full-width" onclick="showScreen('new-case')">
                Create New Case
            </button>
        </div>

        <!-- New Case Screen -->
        <div id="new-case" class="screen">
            <div class="header">
                <button class="btn btn--outline btn--sm back-btn" onclick="showScreen('case-selection')">← Back</button>
                <h2>New Case Setup</h2>
            </div>
            <form id="new-case-form" class="form-container">
                <div class="form-group">
                    <label class="form-label">Case ID</label>
                    <input type="text" class="form-control" id="case-id" value="FIU-2025-003" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">Incident Location</label>
                    <input type="text" class="form-control" id="incident-location" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Incident Date</label>
                    <input type="date" class="form-control" id="incident-date" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Incident Time</label>
                    <input type="time" class="form-control" id="incident-time" required>
                </div>
                <button type="submit" class="btn btn--primary btn--full-width">Create Case</button>
            </form>
        </div>

        <!-- Witness Setup Screen -->
        <div id="witness-setup" class="screen">
            <div class="header">
                <button class="btn btn--outline btn--sm back-btn" onclick="showScreen('case-selection')">← Back</button>
                <h2>Witness Interview Setup</h2>
            </div>
            <div class="case-info">
                <h4 id="current-case-id">FIU-2025-001</h4>
                <p id="current-case-location">123 Main Street, Apartment 4B</p>
            </div>
            <form id="witness-form" class="form-container">
                <div class="form-group">
                    <label class="form-label">Witness Name</label>
                    <input type="text" class="form-control" id="witness-name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Witness Type</label>
                    <select class="form-control" id="witness-type" required>
                        <option value="">Select type...</option>
                        <option value="Resident">Resident</option>
                        <option value="Neighbor">Neighbor</option>
                        <option value="Passerby">Passerby</option>
                        <option value="Business Owner">Business Owner</option>
                        <option value="Emergency Responder">Emergency Responder</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Contact Information</label>
                    <input type="text" class="form-control" id="witness-contact">
                </div>
                <div class="form-group">
                    <label class="form-label">Interview Environment</label>
                    <select class="form-control" id="interview-environment" required>
                        <option value="">Select environment...</option>
                        <option value="controlled">Controlled (Station/Office)</option>
                        <option value="field">Field (On-site)</option>
                    </select>
                </div>
                <button type="submit" class="btn btn--primary btn--full-width">Start Interview</button>
            </form>
        </div>

        <!-- Recording Screen -->
        <div id="recording-screen" class="screen">
            <div class="header">
                <h2>Recording Interview</h2>
                <div class="recording-status">
                    <span class="recording-indicator"></span>
                    <span id="recording-time">00:00</span>
                </div>
            </div>
            <div class="interview-info">
                <p><strong>Case:</strong> <span id="interview-case-id">FIU-2025-001</span></p>
                <p><strong>Witness:</strong> <span id="interview-witness-name">Sarah Williams</span></p>
            </div>
            <div class="audio-visualization">
                <div class="wave-container">
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                    <div class="wave-bar"></div>
                </div>
            </div>
            <div class="recording-controls">
                <button id="pause-btn" class="btn btn--secondary control-btn" onclick="pauseRecording()">
                    <span class="control-icon">⏸</span>
                    Pause
                </button>
                <button id="resume-btn" class="btn btn--secondary control-btn hidden" onclick="resumeRecording()">
                    <span class="control-icon">▶</span>
                    Resume
                </button>
                <button id="stop-btn" class="btn btn--primary control-btn" onclick="stopRecording()">
                    <span class="control-icon">⏹</span>
                    Stop Interview
                </button>
            </div>
        </div>

        <!-- Transcription Screen -->
        <div id="transcription-screen" class="screen">
            <div class="header">
                <h2>Statement Review</h2>
                <button class="btn btn--outline btn--sm" onclick="showScreen('recording-screen')">← Back to Recording</button>
            </div>
            <div class="transcription-container">
                <div class="transcription-header">
                    <h4>Interview Transcript</h4>
                    <p class="timestamp">Recorded: <span id="transcript-date">June 24, 2025 at 1:36 AM</span></p>
                </div>
                <div class="transcript-content" id="transcript-content">
                    <!-- Transcript will be populated by JavaScript -->
                </div>
            </div>
            <div class="statement-summary">
                <h4>Auto-Generated Summary</h4>
                <div class="summary-content" id="summary-content">
                    <!-- Summary will be populated by JavaScript -->
                </div>
            </div>
            <div class="action-buttons">
                <button class="btn btn--secondary" onclick="showScreen('statement-edit')">Edit Statement</button>
                <button class="btn btn--primary" onclick="finalizeStatement()">Approve & Export</button>
            </div>
        </div>

        <!-- Statement Edit Screen -->
        <div id="statement-edit" class="screen">
            <div class="header">
                <button class="btn btn--outline btn--sm back-btn" onclick="showScreen('transcription-screen')">← Back</button>
                <h2>Edit Statement</h2>
            </div>
            <form id="statement-form" class="form-container">
                <div class="form-group">
                    <label class="form-label">Case Information</label>
                    <input type="text" class="form-control" id="edit-case-info" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">Witness Information</label>
                    <input type="text" class="form-control" id="edit-witness-info" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">Statement Summary</label>
                    <textarea class="form-control" id="edit-statement" rows="10"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Officer Notes</label>
                    <textarea class="form-control" id="officer-notes" rows="4" placeholder="Add any additional notes or observations..."></textarea>
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn--secondary" onclick="showScreen('transcription-screen')">Cancel</button>
                    <button type="submit" class="btn btn--primary">Save Changes</button>
                </div>
            </form>
        </div>

        <!-- Export Screen -->
        <div id="export-screen" class="screen">
            <div class="header">
                <h2>Export Statement</h2>
            </div>
            <div class="export-info">
                <div class="card">
                    <div class="card__body">
                        <h4>Statement Ready for Export</h4>
                        <p><strong>Case:</strong> <span id="export-case-id">FIU-2025-001</span></p>
                        <p><strong>Witness:</strong> <span id="export-witness-name">Sarah Williams</span></p>
                        <p><strong>Date:</strong> <span id="export-date">June 24, 2025</span></p>
                        <p><strong>Officer:</strong> Detective Johnson</p>
                    </div>
                </div>
            </div>
            <div class="export-options">
                <h4>Export Format</h4>
                <div class="export-buttons">
                    <button class="btn btn--primary export-btn" onclick="exportPDF()">
                        <span class="export-icon">📄</span>
                        Export as PDF
                    </button>
                    <button class="btn btn--secondary export-btn" onclick="exportWord()">
                        <span class="export-icon">📝</span>
                        Export as Word
                    </button>
                </div>
            </div>
            <div class="action-buttons">
                <button class="btn btn--outline" onclick="showScreen('start-screen')">Start New Interview</button>
                <button class="btn btn--secondary" onclick="showScreen('case-history')">View Case History</button>
            </div>
        </div>

        <!-- Case History Screen -->
        <div id="case-history" class="screen">
            <div class="header">
                <button class="btn btn--outline btn--sm back-btn" onclick="showScreen('start-screen')">← Back</button>
                <h2>Case History</h2>
            </div>
            <div class="case-list">
                <div class="card case-card">
                    <div class="card__body">
                        <div class="case-header">
                            <h4>FIU-2025-001</h4>
                            <span class="status status--warning">In Progress</span>
                        </div>
                        <p><strong>Location:</strong> 123 Main Street, Apartment 4B</p>
                        <p><strong>Date:</strong> June 20, 2025</p>
                        <p><strong>Interviews:</strong> 1 completed</p>
                    </div>
                </div>
                <div class="card case-card">
                    <div class="card__body">
                        <div class="case-header">
                            <h4>FIU-2025-002</h4>
                            <span class="status status--success">Completed</span>
                        </div>
                        <p><strong>Location:</strong> 456 Oak Avenue</p>
                        <p><strong>Date:</strong> June 18, 2025</p>
                        <p><strong>Interviews:</strong> 3 completed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
