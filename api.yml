openapi: 3.0.3
info:
  title: FIU Witness Interview API
  description: API for Fire Investigation Unit witness interview management system
  version: 1.0.0
  contact:
    name: FIU Development Team
    email: <EMAIL>

servers:
  - url: https://api.fiu.gov/v1
    description: Production server
  - url: https://staging-api.fiu.gov/v1
    description: Staging server
  - url: http://localhost:3001/api/v1
    description: Development server

paths:
  # Authentication Endpoints
  /auth/login:
    post:
      summary: User login
      description: Authenticate user with email and password
      tags:
        - Authentication
      security: [] # No auth required for login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  description: User password
                  example: "SecurePassword123!"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                        properties:
                          id:
                            type: string
                          email:
                            type: string
                          profile:
                            $ref: '#/components/schemas/User'
                      session:
                        type: object
                        description: Supabase session object
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/logout:
    post:
      summary: User logout
      description: Sign out the current user
      tags:
        - Authentication
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Successfully logged out"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /auth/signup:
    post:
      summary: User registration
      description: Register a new user (admin only)
      tags:
        - Authentication
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - full_name
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                password:
                  type: string
                  format: password
                  description: User password (min 8 chars, must include uppercase, lowercase, number)
                full_name:
                  type: string
                  description: User's full name
                badge_number:
                  type: string
                  description: Officer badge number
                department:
                  type: string
                  description: Department name
                role:
                  type: string
                  enum: [officer, admin, supervisor]
                  default: officer
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'

  /auth/reset-password:
    post:
      summary: Reset password
      description: Send password reset email
      tags:
        - Authentication
      security: [] # No auth required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Reset email sent
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/me:
    get:
      summary: Get current user profile
      description: Get the authenticated user's profile information
      tags:
        - Authentication
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # User Management Endpoints
  /users:
    get:
      summary: Get all users
      description: Retrieve list of all users (admin/supervisor only)
      tags:
        - Users
      parameters:
        - name: department
          in: query
          description: Filter by department
          schema:
            type: string
        - name: role
          in: query
          description: Filter by role
          schema:
            type: string
            enum: [officer, admin, supervisor]
        - name: active
          in: query
          description: Filter by active status
          schema:
            type: boolean
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
        '403':
          $ref: '#/components/responses/Forbidden'

  /users/{userId}:
    get:
      summary: Get user by ID
      description: Retrieve specific user information
      tags:
        - Users
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/User'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      summary: Update user
      description: Update user information (admin only or own profile)
      tags:
        - Users
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/User'
        '403':
          $ref: '#/components/responses/Forbidden'

  # Case Management Endpoints
  /cases:
    get:
      summary: Get all cases
      description: Retrieve a list of all fire investigation cases
      tags:
        - Cases
      parameters:
        - name: status
          in: query
          description: Filter cases by status
          schema:
            type: string
            enum: [In Progress, Completed]
        - name: officer
          in: query
          description: Filter cases by assigned officer
          schema:
            type: string
        - name: limit
          in: query
          description: Number of cases to return
          schema:
            type: integer
            default: 50
        - name: offset
          in: query
          description: Number of cases to skip
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of cases retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  cases:
                    type: array
                    items:
                      $ref: '#/components/schemas/Case'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new case
      description: Create a new fire investigation case
      tags:
        - Cases
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCaseRequest'
      responses:
        '201':
          description: Case created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}:
    get:
      summary: Get case by ID
      description: Retrieve a specific case by its ID
      tags:
        - Cases
      parameters:
        - name: caseId
          in: path
          required: true
          description: Case ID
          schema:
            type: string
      responses:
        '200':
          description: Case retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update case
      description: Update an existing case
      tags:
        - Cases
      parameters:
        - name: caseId
          in: path
          required: true
          description: Case ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCaseRequest'
      responses:
        '200':
          description: Case updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Interview Management Endpoints
  /cases/{caseId}/interviews:
    get:
      summary: Get interviews for a case
      description: Retrieve all interviews associated with a specific case
      tags:
        - Interviews
      parameters:
        - name: caseId
          in: path
          required: true
          description: Case ID
          schema:
            type: string
      responses:
        '200':
          description: Interviews retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  interviews:
                    type: array
                    items:
                      $ref: '#/components/schemas/Interview'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new interview
      description: Create a new witness interview for a case
      tags:
        - Interviews
      parameters:
        - name: caseId
          in: path
          required: true
          description: Case ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInterviewRequest'
      responses:
        '201':
          description: Interview created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interview'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /interviews/{interviewId}:
    get:
      summary: Get interview by ID
      description: Retrieve a specific interview by its ID
      tags:
        - Interviews
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Interview retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interview'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update interview
      description: Update an existing interview
      tags:
        - Interviews
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInterviewRequest'
      responses:
        '200':
          description: Interview updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interview'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Real-time Transcription WebSocket
  /ws/transcription/{interviewId}:
    get:
      summary: WebSocket endpoint for real-time transcription
      description: |
        WebSocket connection for real-time audio streaming and transcription during interviews.
        
        **Connection Flow:**
        1. Client connects to WebSocket with interview ID
        2. Client sends audio chunks as binary data
        3. Server responds with real-time transcription segments
        4. Connection closes when interview ends
        
        **Message Types:**
        - Audio data: Binary WebSocket frames containing audio chunks
        - Transcription: JSON messages with speaker identification and text
        - Control: JSON messages for pause/resume/stop commands
      tags:
        - Transcription
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '101':
          description: WebSocket connection established
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'

  # Transcription Management
  /interviews/{interviewId}/transcription:
    get:
      summary: Get interview transcription
      description: Retrieve the complete transcription for an interview
      tags:
        - Transcription
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Transcription retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TranscriptionData'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Generate transcription summary
      description: Generate an AI summary of the interview transcription
      tags:
        - Transcription
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Summary generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  summary:
                    type: string
                    description: AI-generated summary of the interview
                  generatedAt:
                    type: string
                    format: date-time
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Statement Management
  /interviews/{interviewId}/statement:
    get:
      summary: Get interview statement
      description: Retrieve the formal statement for an interview
      tags:
        - Statements
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Statement retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Statement'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update interview statement
      description: Update the formal statement for an interview
      tags:
        - Statements
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStatementRequest'
      responses:
        '200':
          description: Statement updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Statement'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Export Endpoints
  /interviews/{interviewId}/export/pdf:
    post:
      summary: Export statement as PDF
      description: Generate and download a PDF version of the interview statement
      tags:
        - Export
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: PDF generated successfully
          content:
            application/pdf:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment filename
              schema:
                type: string
                example: 'attachment; filename="FIU-2025-001_Sarah_Williams_Statement.pdf"'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /interviews/{interviewId}/export/docx:
    post:
      summary: Export statement as Word document
      description: Generate and download a Word document version of the interview statement
      tags:
        - Export
      parameters:
        - name: interviewId
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Word document generated successfully
          content:
            application/vnd.openxmlformats-officedocument.wordprocessingml.document:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment filename
              schema:
                type: string
                example: 'attachment; filename="FIU-2025-001_Sarah_Williams_Statement.docx"'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    User:
      type: object
      required:
        - id
        - email
        - full_name
        - role
        - is_active
      properties:
        id:
          type: string
          description: Unique user identifier
          example: "550e8400-e29b-41d4-a716-************"
        email:
          type: string
          format: email
          description: User email address
          example: "<EMAIL>"
        full_name:
          type: string
          description: User's full name
          example: "Detective Johnson"
        badge_number:
          type: string
          description: Officer badge number
          example: "FIU001"
        department:
          type: string
          description: Department name
          example: "Fire Investigation Unit"
        role:
          type: string
          enum: [officer, admin, supervisor]
          description: User role
          example: "officer"
        is_active:
          type: boolean
          description: Account status
          example: true
        created_at:
          type: string
          format: date-time
          description: Account creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp

    CreateUserRequest:
      type: object
      required:
        - email
        - full_name
      properties:
        email:
          type: string
          format: email
          description: User email address
        full_name:
          type: string
          description: User's full name
        badge_number:
          type: string
          description: Officer badge number
        department:
          type: string
          description: Department name
        role:
          type: string
          enum: [officer, admin, supervisor]
          default: officer
          description: User role

    UpdateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User email address
        full_name:
          type: string
          description: User's full name
        badge_number:
          type: string
          description: Officer badge number
        department:
          type: string
          description: Department name
        role:
          type: string
          enum: [officer, admin, supervisor]
          description: User role
        is_active:
          type: boolean
          description: Account status

    Case:
      type: object
      required:
        - id
        - incidentLocation
        - incidentDate
        - incidentTime
        - assignedOfficerId
        - status
      properties:
        id:
          type: string
          description: Unique case identifier
          example: "FIU-2025-001"
        incidentLocation:
          type: string
          description: Location where the incident occurred
          example: "123 Main Street, Apartment 4B"
        incidentDate:
          type: string
          format: date
          description: Date of the incident
          example: "2025-06-20"
        incidentTime:
          type: string
          format: time
          description: Time of the incident
          example: "14:30"
        assignedOfficerId:
          type: string
          description: ID of assigned investigating officer
          example: "550e8400-e29b-41d4-a716-************"
        assignedOfficer:
          $ref: '#/components/schemas/User'
          description: Assigned officer details (populated when joined)
        status:
          type: string
          enum: [In Progress, Completed]
          description: Current status of the case
        createdAt:
          type: string
          format: date-time
          description: Case creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    CreateCaseRequest:
      type: object
      required:
        - incidentLocation
        - incidentDate
        - incidentTime
        - assignedOfficerId
      properties:
        incidentLocation:
          type: string
          description: Location where the incident occurred
        incidentDate:
          type: string
          format: date
          description: Date of the incident
        incidentTime:
          type: string
          format: time
          description: Time of the incident
        assignedOfficerId:
          type: string
          description: ID of assigned investigating officer

    UpdateCaseRequest:
      type: object
      properties:
        incidentLocation:
          type: string
          description: Location where the incident occurred
        incidentDate:
          type: string
          format: date
          description: Date of the incident
        incidentTime:
          type: string
          format: time
          description: Time of the incident
        officer:
          type: string
          description: Assigned investigating officer
        status:
          type: string
          enum: [In Progress, Completed]
          description: Current status of the case

    Witness:
      type: object
      required:
        - name
        - type
        - contact
      properties:
        name:
          type: string
          description: Full name of the witness
          example: "Sarah Williams"
        type:
          type: string
          enum: [Resident, Neighbor, Passerby, Business Owner, Emergency Responder]
          description: Type of witness
          example: "Neighbor"
        contact:
          type: string
          description: Contact information (phone, email, etc.)
          example: "555-0123"
        environment:
          type: string
          enum: [controlled, field]
          description: Interview environment
          example: "field"

    Interview:
      type: object
      required:
        - id
        - caseId
        - witness
        - status
      properties:
        id:
          type: string
          description: Unique interview identifier
          example: "INT-2025-001-001"
        caseId:
          type: string
          description: Associated case ID
          example: "FIU-2025-001"
        witness:
          $ref: '#/components/schemas/Witness'
        status:
          type: string
          enum: [scheduled, in_progress, completed, cancelled]
          description: Current status of the interview
        startTime:
          type: string
          format: date-time
          description: Interview start time
        endTime:
          type: string
          format: date-time
          description: Interview end time
        duration:
          type: integer
          description: Interview duration in seconds
        recordingPath:
          type: string
          description: Path to the audio recording file
        createdAt:
          type: string
          format: date-time
          description: Interview creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp

    CreateInterviewRequest:
      type: object
      required:
        - witness
      properties:
        witness:
          $ref: '#/components/schemas/Witness'
        scheduledTime:
          type: string
          format: date-time
          description: Scheduled interview time

    UpdateInterviewRequest:
      type: object
      properties:
        witness:
          $ref: '#/components/schemas/Witness'
        status:
          type: string
          enum: [scheduled, in_progress, completed, cancelled]
          description: Current status of the interview
        endTime:
          type: string
          format: date-time
          description: Interview end time

    TranscriptSpeaker:
      type: object
      required:
        - id
        - name
        - color
      properties:
        id:
          type: string
          description: Unique speaker identifier
          example: "S1"
        name:
          type: string
          description: Speaker name
          example: "Officer Johnson"
        color:
          type: string
          description: Color code for speaker identification in UI
          example: "#2563eb"

    TranscriptSegment:
      type: object
      required:
        - speaker
        - timestamp
        - text
      properties:
        speaker:
          type: string
          description: Speaker ID reference
          example: "S1"
        timestamp:
          type: string
          description: Timestamp in MM:SS format
          example: "00:15"
        text:
          type: string
          description: Transcribed text
          example: "My name is Sarah Williams. I live in apartment 3A, right next door to where the fire happened."
        confidence:
          type: number
          format: float
          minimum: 0
          maximum: 1
          description: Transcription confidence score

    TranscriptionData:
      type: object
      required:
        - speakers
        - segments
      properties:
        speakers:
          type: array
          items:
            $ref: '#/components/schemas/TranscriptSpeaker'
          description: List of speakers in the interview
        segments:
          type: array
          items:
            $ref: '#/components/schemas/TranscriptSegment'
          description: Chronological list of transcription segments
        totalDuration:
          type: integer
          description: Total duration in seconds
        language:
          type: string
          description: Detected language code
          example: "en-US"

    Statement:
      type: object
      required:
        - id
        - interviewId
        - content
      properties:
        id:
          type: string
          description: Unique statement identifier
        interviewId:
          type: string
          description: Associated interview ID
        content:
          type: string
          description: Formal statement content
        officerNotes:
          type: string
          description: Additional officer notes
        createdAt:
          type: string
          format: date-time
          description: Statement creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
        version:
          type: integer
          description: Statement version number

    UpdateStatementRequest:
      type: object
      required:
        - content
      properties:
        content:
          type: string
          description: Updated statement content
        officerNotes:
          type: string
          description: Additional officer notes

    # WebSocket Message Schemas
    TranscriptionMessage:
      type: object
      required:
        - type
        - data
      properties:
        type:
          type: string
          enum: [transcription, control, error]
          description: Message type
        data:
          oneOf:
            - $ref: '#/components/schemas/TranscriptSegment'
            - $ref: '#/components/schemas/ControlMessage'
            - $ref: '#/components/schemas/ErrorMessage'

    ControlMessage:
      type: object
      required:
        - action
      properties:
        action:
          type: string
          enum: [start, pause, resume, stop]
          description: Control action
        timestamp:
          type: string
          format: date-time
          description: Action timestamp

    ErrorMessage:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details

    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        details:
          type: object
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp

  responses:
    BadRequest:
      description: Bad request - invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INVALID_INPUT"
            message: "The provided input parameters are invalid"
            timestamp: "2025-06-24T10:30:00Z"

    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "UNAUTHORIZED"
            message: "Authentication required"
            timestamp: "2025-06-24T10:30:00Z"

    Forbidden:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "FORBIDDEN"
            message: "Insufficient permissions to access this resource"
            timestamp: "2025-06-24T10:30:00Z"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "RESOURCE_NOT_FOUND"
            message: "The requested resource was not found"
            timestamp: "2025-06-24T10:30:00Z"

    TooManyRequests:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "RATE_LIMIT_EXCEEDED"
            message: "Too many requests. Please try again later."
            timestamp: "2025-06-24T10:30:00Z"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INTERNAL_ERROR"
            message: "An internal server error occurred"
            timestamp: "2025-06-24T10:30:00Z"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for API authentication

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for service authentication

security:
  - BearerAuth: []
  - ApiKeyAuth: []

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management and profiles
  - name: Cases
    description: Fire investigation case management
  - name: Interviews
    description: Witness interview management
  - name: Transcription
    description: Real-time transcription and audio processing
  - name: Statements
    description: Formal statement management
  - name: Export
    description: Document export functionality
