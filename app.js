// FIU Interview System JavaScript

// Application state
let currentState = {
    currentCase: null,
    currentWitness: null,
    isRecording: false,
    isPaused: false,
    recordingStartTime: null,
    recordingPausedTime: 0,
    transcriptData: null,
    summaryData: null
};

// Mock data from the application
const mockData = {
    cases: [
        {
            id: "FIU-2025-001",
            incidentLocation: "123 Main Street, Apartment 4B",
            incidentDate: "2025-06-20",
            incidentTime: "14:30",
            officer: "<PERSON>",
            status: "In Progress"
        },
        {
            id: "FIU-2025-002", 
            incidentLocation: "456 Oak Avenue, Single Family Home",
            incidentDate: "2025-06-18",
            incidentTime: "09:15",
            officer: "<PERSON>",
            status: "Completed"
        }
    ],
    witnesses: [
        {
            name: "<PERSON>",
            type: "Neighbor",
            contact: "555-0123",
            address: "125 Main Street, Apt 3A"
        },
        {
            name: "<PERSON>",
            type: "Resident",
            contact: "555-0456", 
            address: "123 Main Street, Apt 4A"
        }
    ],
    transcription: {
        speakers: [
            {id: "S1", name: "Officer <PERSON>", color: "#2563eb"},
            {id: "S2", name: "<PERSON>", color: "#dc3545"}
        ],
        segments: [
            {speaker: "S1", timestamp: "00:00", text: "This is Detective <PERSON> with the Fire Investigation Unit. Today is June 24th, 2025, and I'm conducting an interview regarding the incident at 123 Main Street. Could you please state your name for the record?"},
            {speaker: "S2", timestamp: "00:15", text: "My name is <PERSON> Williams. I live in apartment 3A, right next door to where the fire happened."},
            {speaker: "S1", timestamp: "00:25", text: "Thank you, Sarah. Can you tell me what you observed on the day of the incident?"},
            {speaker: "S2", timestamp: "00:32", text: "I was home around 2:30 PM when I heard a loud bang from next door. At first I thought maybe someone dropped something heavy, but then I started smelling smoke."},
            {speaker: "S1", timestamp: "00:45", text: "What did you do when you smelled the smoke?"},
            {speaker: "S2", timestamp: "00:48", text: "I immediately went to my window and looked outside. I could see smoke coming from their kitchen window. That's when I called 911 and then knocked on their door to make sure everyone was okay."}
        ]
    },
    summary: "Witness Sarah Williams, residing in apartment 3A at 125 Main Street, reported hearing a loud bang from the adjacent apartment 4B at approximately 2:30 PM on June 20th, 2025. Upon detecting smoke odor, she observed smoke emanating from the kitchen window of the affected unit. Williams promptly contacted emergency services and attempted to ensure the safety of the occupants. Her account provides valuable timeline information and corroborates the initial fire location assessment.",
    officer: {
        name: "Detective Johnson",
        badge: "FIU-2847",
        department: "Fire Investigation Unit",
        district: "Central Division"
    }
};

// Screen navigation
function showScreen(screenId) {
    // Hide all screens
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    
    // Show target screen
    document.getElementById(screenId).classList.add('active');
    
    // Initialize screen-specific functionality
    initializeScreen(screenId);
}

function initializeScreen(screenId) {
    switch(screenId) {
        case 'witness-setup':
            initializeWitnessSetup();
            break;
        case 'recording-screen':
            initializeRecording();
            break;
        case 'transcription-screen':
            initializeTranscription();
            break;
        case 'statement-edit':
            initializeStatementEdit();
            break;
        case 'export-screen':
            initializeExport();
            break;
    }
}

// Case selection
function selectCase(caseId) {
    const selectedCase = mockData.cases.find(c => c.id === caseId);
    if (selectedCase) {
        currentState.currentCase = selectedCase;
        showScreen('witness-setup');
    }
}

function initializeWitnessSetup() {
    if (currentState.currentCase) {
        document.getElementById('current-case-id').textContent = currentState.currentCase.id;
        document.getElementById('current-case-location').textContent = currentState.currentCase.incidentLocation;
    }
}

// Form handlers
document.addEventListener('DOMContentLoaded', function() {
    // New case form
    const newCaseForm = document.getElementById('new-case-form');
    if (newCaseForm) {
        newCaseForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newCase = {
                id: document.getElementById('case-id').value,
                incidentLocation: document.getElementById('incident-location').value,
                incidentDate: document.getElementById('incident-date').value,
                incidentTime: document.getElementById('incident-time').value,
                officer: mockData.officer.name,
                status: "In Progress"
            };
            
            currentState.currentCase = newCase;
            showScreen('witness-setup');
        });
    }
    
    // Witness form
    const witnessForm = document.getElementById('witness-form');
    if (witnessForm) {
        witnessForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const witness = {
                name: document.getElementById('witness-name').value,
                type: document.getElementById('witness-type').value,
                contact: document.getElementById('witness-contact').value,
                environment: document.getElementById('interview-environment').value
            };
            
            currentState.currentWitness = witness;
            showScreen('recording-screen');
        });
    }
    
    // Statement form
    const statementForm = document.getElementById('statement-form');
    if (statementForm) {
        statementForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Save the edited statement
            currentState.summaryData = document.getElementById('edit-statement').value;
            
            showScreen('export-screen');
        });
    }
});

// Recording functionality
let recordingTimer;
let recordingStartTime;
let pausedDuration = 0;

function initializeRecording() {
    if (currentState.currentWitness) {
        document.getElementById('interview-witness-name').textContent = currentState.currentWitness.name;
    }
    if (currentState.currentCase) {
        document.getElementById('interview-case-id').textContent = currentState.currentCase.id;
    }
    
    // Start recording automatically
    startRecording();
}

function startRecording() {
    currentState.isRecording = true;
    currentState.isPaused = false;
    recordingStartTime = Date.now();
    pausedDuration = 0;
    
    // Start timer
    recordingTimer = setInterval(updateRecordingTime, 1000);
    
    // Show recording UI
    document.getElementById('pause-btn').classList.remove('hidden');
    document.getElementById('resume-btn').classList.add('hidden');
    
    // Start audio visualization
    startAudioVisualization();
}

function pauseRecording() {
    currentState.isPaused = true;
    clearInterval(recordingTimer);
    
    // Update UI
    document.getElementById('pause-btn').classList.add('hidden');
    document.getElementById('resume-btn').classList.remove('hidden');
    
    // Stop audio visualization
    stopAudioVisualization();
}

function resumeRecording() {
    currentState.isPaused = false;
    
    // Calculate paused time
    const pauseStart = Date.now();
    
    // Restart timer
    recordingTimer = setInterval(updateRecordingTime, 1000);
    
    // Update UI
    document.getElementById('pause-btn').classList.remove('hidden');
    document.getElementById('resume-btn').classList.add('hidden');
    
    // Restart audio visualization
    startAudioVisualization();
}

function stopRecording() {
    currentState.isRecording = false;
    clearInterval(recordingTimer);
    
    // Stop audio visualization
    stopAudioVisualization();
    
    // Show processing screen
    showScreen('loading-screen');
    
    // Simulate processing time
    setTimeout(() => {
        // Populate transcript data
        currentState.transcriptData = mockData.transcription;
        currentState.summaryData = mockData.summary;
        
        showScreen('transcription-screen');
    }, 3000);
}

function updateRecordingTime() {
    const elapsed = Date.now() - recordingStartTime - pausedDuration;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    
    document.getElementById('recording-time').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Audio visualization
function startAudioVisualization() {
    const waveBars = document.querySelectorAll('.wave-bar');
    waveBars.forEach(bar => {
        bar.style.animationPlayState = 'running';
    });
}

function stopAudioVisualization() {
    const waveBars = document.querySelectorAll('.wave-bar');
    waveBars.forEach(bar => {
        bar.style.animationPlayState = 'paused';
    });
}

// Transcription display
function initializeTranscription() {
    if (currentState.transcriptData) {
        displayTranscript();
    }
    
    if (currentState.summaryData) {
        displaySummary();
    }
    
    // Update timestamp
    const now = new Date();
    document.getElementById('transcript-date').textContent = 
        now.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        }) + ' at ' + now.toLocaleTimeString('en-US');
}

function displayTranscript() {
    const container = document.getElementById('transcript-content');
    container.innerHTML = '';
    
    currentState.transcriptData.segments.forEach(segment => {
        const segmentDiv = document.createElement('div');
        segmentDiv.className = 'transcript-segment';
        
        const speaker = currentState.transcriptData.speakers.find(s => s.id === segment.speaker);
        
        segmentDiv.innerHTML = `
            <div class="speaker-info" style="border-right-color: ${speaker.color}">
                ${speaker.name}
                <span class="speaker-timestamp">${segment.timestamp}</span>
            </div>
            <div class="speaker-text">${segment.text}</div>
        `;
        
        container.appendChild(segmentDiv);
    });
}

function displaySummary() {
    document.getElementById('summary-content').textContent = currentState.summaryData;
}

// Statement editing
function initializeStatementEdit() {
    if (currentState.currentCase && currentState.currentWitness) {
        document.getElementById('edit-case-info').value = 
            `${currentState.currentCase.id} - ${currentState.currentCase.incidentLocation}`;
        document.getElementById('edit-witness-info').value = 
            `${currentState.currentWitness.name} (${currentState.currentWitness.type})`;
    }
    
    if (currentState.summaryData) {
        document.getElementById('edit-statement').value = currentState.summaryData;
    }
}

function finalizeStatement() {
    showScreen('export-screen');
}

// Export functionality
function initializeExport() {
    if (currentState.currentCase) {
        document.getElementById('export-case-id').textContent = currentState.currentCase.id;
    }
    
    if (currentState.currentWitness) {
        document.getElementById('export-witness-name').textContent = currentState.currentWitness.name;
    }
    
    const now = new Date();
    document.getElementById('export-date').textContent = 
        now.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
}

function exportPDF() {
    // Simulate PDF export
    alert('Statement exported as PDF successfully!\n\nFile: ' + 
          `${currentState.currentCase.id}_${currentState.currentWitness.name.replace(' ', '_')}_Statement.pdf`);
    
    // Reset state for new interview
    resetApplicationState();
    showScreen('start-screen');
}

function exportWord() {
    // Simulate Word export
    alert('Statement exported as Word document successfully!\n\nFile: ' + 
          `${currentState.currentCase.id}_${currentState.currentWitness.name.replace(' ', '_')}_Statement.docx`);
    
    // Reset state for new interview
    resetApplicationState();
    showScreen('start-screen');
}

function resetApplicationState() {
    currentState = {
        currentCase: null,
        currentWitness: null,
        isRecording: false,
        isPaused: false,
        recordingStartTime: null,
        recordingPausedTime: 0,
        transcriptData: null,
        summaryData: null
    };
    
    // Clear forms
    document.querySelectorAll('form').forEach(form => {
        form.reset();
    });
    
    // Reset recording UI
    document.getElementById('recording-time').textContent = '00:00';
    document.getElementById('pause-btn').classList.remove('hidden');
    document.getElementById('resume-btn').classList.add('hidden');
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
}

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    // Show start screen
    showScreen('start-screen');
    
    // Set up auto-fill for demo purposes
    setTimeout(() => {
        // Pre-fill witness form with demo data
        const witnessNameField = document.getElementById('witness-name');
        const witnessTypeField = document.getElementById('witness-type');
        const witnessContactField = document.getElementById('witness-contact');
        
        if (witnessNameField) {
            witnessNameField.value = mockData.witnesses[0].name;
        }
        if (witnessTypeField) {
            witnessTypeField.value = mockData.witnesses[0].type;
        }
        if (witnessContactField) {
            witnessContactField.value = mockData.witnesses[0].contact;
        }
    }, 500);
});

// Handle browser back button
window.addEventListener('popstate', function(event) {
    if (currentState.isRecording) {
        // Prevent navigation during recording
        event.preventDefault();
        alert('Please stop the recording before navigating away.');
        return false;
    }
});

// Prevent accidental page refresh during recording
window.addEventListener('beforeunload', function(event) {
    if (currentState.isRecording) {
        event.preventDefault();
        event.returnValue = 'Recording in progress. Are you sure you want to leave?';
        return 'Recording in progress. Are you sure you want to leave?';
    }
});

// Service worker registration for offline capabilities (simulated)
if ('serviceWorker' in navigator) {
    // In a real app, you would register a service worker here
    console.log('Service Worker support detected - offline capabilities available');
}
