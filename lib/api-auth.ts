// API authentication and authorization helpers
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { UserService } from './supabase';
import type { User } from '@/types/database';

export interface AuthenticatedRequest extends NextRequest {
  user?: User;
  userId?: string;
  userRole?: string;
  userDepartment?: string;
}

export interface ApiError {
  error: string;
  message: string;
  code?: string;
}

// Get authenticated user from request
export async function getAuthenticatedUser(request: NextRequest): Promise<User | null> {
  try {
    // First try to get session from cookies (for server-side requests)
    const supabase = createRouteHandlerClient({ cookies });

    let session = null;
    let user = null;

    // Try to get session from cookies first
    try {
      const sessionResult = await supabase.auth.getSession();
      session = sessionResult.data.session;
      user = session?.user;
    } catch (cookieError) {
      console.log('Cookie session not available, trying Authorization header');
    }

    // If no session from cookies, try Authorization header
    if (!user) {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        try {
          const { data: { user: headerUser }, error } = await supabase.auth.getUser(token);
          if (!error && headerUser) {
            user = headerUser;
          }
        } catch (headerError) {
          console.log('Authorization header authentication failed:', headerError);
        }
      }
    }

    if (!user?.email) {
      return null;
    }

    // Get user profile from our users table
    const userProfile = await UserService.getUserByEmail(user.email);
    return userProfile;
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

// Middleware wrapper for API routes that require authentication
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    requiredRole?: string;
    requiredPermissions?: string[];
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const user = await getAuthenticatedUser(request);

      if (!user) {
        return NextResponse.json(
          {
            error: 'Unauthorized',
            message: 'Authentication required',
          } as ApiError,
          { status: 401 }
        );
      }

      // Check role requirement
      if (options.requiredRole && user.role !== options.requiredRole) {
        // Allow admin to access everything
        if (user.role !== 'admin') {
          return NextResponse.json(
            {
              error: 'Forbidden',
              message: `Required role: ${options.requiredRole}`,
            } as ApiError,
            { status: 403 }
          );
        }
      }

      // Check permissions (custom logic can be added here)
      if (options.requiredPermissions) {
        const hasPermissions = checkUserPermissions(user, options.requiredPermissions);
        if (!hasPermissions) {
          return NextResponse.json(
            {
              error: 'Forbidden',
              message: 'Insufficient permissions',
            } as ApiError,
            { status: 403 }
          );
        }
      }

      // Add user info to request
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = user;
      authenticatedRequest.userId = user.id;
      authenticatedRequest.userRole = user.role;
      authenticatedRequest.userDepartment = user.department;

      return await handler(authenticatedRequest);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: 'Authentication check failed',
        } as ApiError,
        { status: 500 }
      );
    }
  };
}

// Check if user has required permissions
function checkUserPermissions(user: User, requiredPermissions: string[]): boolean {
  // Admin has all permissions
  if (user.role === 'admin') {
    return true;
  }

  // Define role-based permissions
  const rolePermissions: Record<string, string[]> = {
    officer: [
      'cases:read:own',
      'cases:create',
      'interviews:read:own',
      'interviews:create',
      'interviews:update:own',
      'transcriptions:read:own',
      'transcriptions:create',
      'statements:read:own',
      'statements:create',
      'statements:update:own',
      'exports:create',
    ],
    supervisor: [
      'cases:read:all',
      'cases:read:own',
      'cases:create',
      'interviews:read:all',
      'interviews:read:own',
      'interviews:create',
      'interviews:update:own',
      'transcriptions:read:all',
      'transcriptions:read:own',
      'transcriptions:create',
      'statements:read:all',
      'statements:read:own',
      'statements:create',
      'statements:update:own',
      'exports:create',
      'reports:read',
    ],
    admin: ['*'], // Admin has all permissions
  };

  const userPermissions = rolePermissions[user.role] || [];

  // Check if user has all required permissions
  return requiredPermissions.every(permission => 
    userPermissions.includes('*') || userPermissions.includes(permission)
  );
}

// Helper to check if user can access specific resource
export async function canAccessCase(user: User, caseId: string): Promise<boolean> {
  if (user.role === 'admin' || user.role === 'supervisor') {
    return true;
  }

  // Officers can only access cases assigned to them
  try {
    const caseData = await UserService.getUserById(user.id);
    // This would need to be implemented to check case assignment
    // For now, return true for officers
    return user.role === 'officer';
  } catch (error) {
    return false;
  }
}

export async function canAccessInterview(user: User, interviewId: string): Promise<boolean> {
  if (user.role === 'admin' || user.role === 'supervisor') {
    return true;
  }

  // Officers can only access interviews they conducted or for cases assigned to them
  // This would need to be implemented with proper database queries
  return user.role === 'officer';
}

// Standard API response helpers
export function createSuccessResponse<T>(data: T, status = 200): NextResponse {
  return NextResponse.json({ success: true, data }, { status });
}

export function createErrorResponse(
  error: string,
  message: string,
  status = 400,
  code?: string
): NextResponse {
  return NextResponse.json(
    {
      error,
      message,
      code,
      timestamp: new Date().toISOString(),
    } as ApiError & { timestamp: string },
    { status }
  );
}

// Validation helpers
export function validateRequiredFields(
  data: Record<string, any>,
  requiredFields: string[]
): string[] {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      missingFields.push(field);
    }
  }
  
  return missingFields;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

// Rate limiting helper (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests = 100,
  windowMs = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < now) {
      rateLimitMap.delete(key);
    }
  }
  
  const current = rateLimitMap.get(identifier);
  
  if (!current || current.resetTime < now) {
    // New window
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs,
    });
    
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: now + windowMs,
    };
  }
  
  if (current.count >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: current.resetTime,
    };
  }
  
  current.count++;
  
  return {
    allowed: true,
    remaining: maxRequests - current.count,
    resetTime: current.resetTime,
  };
}
