-- FIU Witness Interview Database Initialization Script
-- PostgreSQL/Supabase Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better type safety
CREATE TYPE user_role AS ENUM ('officer', 'admin', 'supervisor');
CREATE TYPE case_status AS ENUM ('In Progress', 'Completed');
CREATE TYPE interview_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE witness_type AS ENUM ('Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder');
CREATE TYPE interview_environment AS ENUM ('controlled', 'field');
CREATE TYPE transcription_status AS ENUM ('pending', 'processing', 'completed', 'failed');
CREATE TYPE export_type AS ENUM ('pdf', 'docx');

-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    badge_number VARCHAR(50) UNIQUE,
    department VARCHAR(100),
    role user_role NOT NULL DEFAULT 'officer',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create cases table
CREATE TABLE cases (
    id VARCHAR(50) PRIMARY KEY,
    incident_location TEXT NOT NULL,
    incident_date DATE NOT NULL,
    incident_time TIME NOT NULL,
    assigned_officer_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    status case_status NOT NULL DEFAULT 'In Progress',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create interviews table
CREATE TABLE interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    case_id VARCHAR(50) NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    interviewing_officer_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    witness_name VARCHAR(255) NOT NULL,
    witness_type witness_type NOT NULL,
    witness_contact VARCHAR(255),
    interview_environment interview_environment,
    status interview_status NOT NULL DEFAULT 'scheduled',
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    duration_seconds INTEGER,
    recording_path TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints
    CONSTRAINT check_end_time_after_start CHECK (end_time IS NULL OR start_time IS NULL OR end_time > start_time),
    CONSTRAINT check_duration_positive CHECK (duration_seconds IS NULL OR duration_seconds > 0)
);

-- Create transcriptions table (simplified - stores complete transcription as JSONB)
CREATE TABLE transcriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE UNIQUE,
    transcription_data JSONB NOT NULL,
    language VARCHAR(10) DEFAULT 'en-US',
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_status transcription_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create statements table
CREATE TABLE statements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE UNIQUE,
    content TEXT NOT NULL,
    officer_notes TEXT,
    version INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_version_positive CHECK (version > 0)
);

-- Create audio_recordings table
CREATE TABLE audio_recordings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE UNIQUE,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    duration_seconds INTEGER,
    format VARCHAR(10),
    sample_rate INTEGER,
    channels INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_file_size_positive CHECK (file_size IS NULL OR file_size > 0),
    CONSTRAINT check_duration_positive CHECK (duration_seconds IS NULL OR duration_seconds > 0),
    CONSTRAINT check_sample_rate_positive CHECK (sample_rate IS NULL OR sample_rate > 0),
    CONSTRAINT check_channels_positive CHECK (channels IS NULL OR channels > 0)
);

-- Create export_logs table
CREATE TABLE export_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE,
    export_type export_type NOT NULL,
    file_path TEXT,
    exported_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_badge_number ON users(badge_number);
CREATE INDEX idx_users_department ON users(department);
CREATE INDEX idx_cases_status ON cases(status);
CREATE INDEX idx_cases_assigned_officer ON cases(assigned_officer_id);
CREATE INDEX idx_cases_incident_date ON cases(incident_date);
CREATE INDEX idx_interviews_case_id ON interviews(case_id);
CREATE INDEX idx_interviews_officer_id ON interviews(interviewing_officer_id);
CREATE INDEX idx_interviews_status ON interviews(status);
CREATE INDEX idx_interviews_start_time ON interviews(start_time);
CREATE INDEX idx_transcriptions_interview_id ON transcriptions(interview_id);
CREATE INDEX idx_transcriptions_status ON transcriptions(processing_status);
CREATE INDEX idx_statements_interview_id ON statements(interview_id);
CREATE INDEX idx_audio_recordings_interview_id ON audio_recordings(interview_id);
CREATE INDEX idx_export_logs_interview_id ON export_logs(interview_id);
CREATE INDEX idx_export_logs_user_id ON export_logs(exported_by_user_id);
CREATE INDEX idx_export_logs_created_at ON export_logs(created_at);

-- Create JSONB indexes for transcription data queries
CREATE INDEX idx_transcriptions_data_speakers ON transcriptions USING GIN ((transcription_data->'speakers'));
CREATE INDEX idx_transcriptions_data_segments ON transcriptions USING GIN ((transcription_data->'segments'));

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at timestamp updates
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cases_updated_at
    BEFORE UPDATE ON cases
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_interviews_updated_at
    BEFORE UPDATE ON interviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transcriptions_updated_at
    BEFORE UPDATE ON transcriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_statements_updated_at
    BEFORE UPDATE ON statements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment statement version on content update
CREATE OR REPLACE FUNCTION increment_statement_version()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.content IS DISTINCT FROM NEW.content THEN
        NEW.version = OLD.version + 1;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for statement version increment
CREATE TRIGGER increment_statement_version_trigger
    BEFORE UPDATE ON statements
    FOR EACH ROW
    EXECUTE FUNCTION increment_statement_version();

-- Create function to validate transcription data structure
CREATE OR REPLACE FUNCTION validate_transcription_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate that transcription_data has required structure
    IF NOT (NEW.transcription_data ? 'speakers' AND NEW.transcription_data ? 'segments') THEN
        RAISE EXCEPTION 'Transcription data must contain speakers and segments arrays';
    END IF;

    -- Validate speakers array structure
    IF NOT (jsonb_typeof(NEW.transcription_data->'speakers') = 'array') THEN
        RAISE EXCEPTION 'Speakers must be an array';
    END IF;

    -- Validate segments array structure
    IF NOT (jsonb_typeof(NEW.transcription_data->'segments') = 'array') THEN
        RAISE EXCEPTION 'Segments must be an array';
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to validate transcription data
CREATE TRIGGER validate_transcription_data_trigger
    BEFORE INSERT OR UPDATE ON transcriptions
    FOR EACH ROW
    EXECUTE FUNCTION validate_transcription_data();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE transcriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE statements ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_recordings ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic policies - adjust based on your auth requirements)
-- Note: These policies assume you have user authentication set up in Supabase

-- Users: Users can view their own data and colleagues in same department
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can view colleagues in same department" ON users
    FOR SELECT USING (
        department = (SELECT department FROM users WHERE id::text = auth.uid()::text)
        OR auth.jwt() ->> 'role' = 'admin'
    );

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

CREATE POLICY "Admins can manage all users" ON users
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Cases: Officers can access their assigned cases
CREATE POLICY "Officers can view their assigned cases" ON cases
    FOR SELECT USING (
        assigned_officer_id::text = auth.uid()::text
        OR auth.jwt() ->> 'role' = 'admin'
        OR auth.jwt() ->> 'role' = 'supervisor'
    );

CREATE POLICY "Officers can update their assigned cases" ON cases
    FOR UPDATE USING (
        assigned_officer_id::text = auth.uid()::text
        OR auth.jwt() ->> 'role' = 'admin'
    );

CREATE POLICY "Officers can insert new cases" ON cases
    FOR INSERT WITH CHECK (
        assigned_officer_id::text = auth.uid()::text
        OR auth.jwt() ->> 'role' = 'admin'
    );

-- Interviews: Access based on case access and interviewing officer
CREATE POLICY "Users can view interviews for accessible cases" ON interviews
    FOR SELECT USING (
        interviewing_officer_id::text = auth.uid()::text
        OR EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = interviews.case_id
            AND (cases.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin'
                 OR auth.jwt() ->> 'role' = 'supervisor')
        )
    );

CREATE POLICY "Users can modify interviews for accessible cases" ON interviews
    FOR ALL USING (
        interviewing_officer_id::text = auth.uid()::text
        OR EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = interviews.case_id
            AND (cases.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Transcription data: Access based on interview access
CREATE POLICY "Users can access transcriptions for accessible interviews" ON transcriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = transcriptions.interview_id
            AND (i.interviewing_officer_id::text = auth.uid()::text
                 OR c.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin'
                 OR auth.jwt() ->> 'role' = 'supervisor')
        )
    );

-- Statements: Access based on interview access
CREATE POLICY "Users can access statements for accessible interviews" ON statements
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = statements.interview_id
            AND (i.interviewing_officer_id::text = auth.uid()::text
                 OR c.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin'
                 OR auth.jwt() ->> 'role' = 'supervisor')
        )
    );

-- Audio recordings: Access based on interview access
CREATE POLICY "Users can access audio recordings for accessible interviews" ON audio_recordings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = audio_recordings.interview_id
            AND (i.interviewing_officer_id::text = auth.uid()::text
                 OR c.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin'
                 OR auth.jwt() ->> 'role' = 'supervisor')
        )
    );

-- Export logs: Access based on interview access
CREATE POLICY "Users can access export logs for accessible interviews" ON export_logs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = export_logs.interview_id
            AND (i.interviewing_officer_id::text = auth.uid()::text
                 OR c.assigned_officer_id::text = auth.uid()::text
                 OR auth.jwt() ->> 'role' = 'admin'
                 OR auth.jwt() ->> 'role' = 'supervisor')
        )
    );

-- Insert sample data for testing
-- First, insert sample users
INSERT INTO users (id, email, full_name, badge_number, department, role) VALUES
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Detective Johnson', 'FIU001', 'Fire Investigation Unit', 'officer'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Detective Smith', 'FIU002', 'Fire Investigation Unit', 'officer'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Supervisor Brown', 'FIU003', 'Fire Investigation Unit', 'supervisor');

-- Insert sample cases
INSERT INTO cases (id, incident_location, incident_date, incident_time, assigned_officer_id, status) VALUES
('FIU-2025-001', '123 Main Street, Apartment 4B', '2025-06-20', '14:30', '550e8400-e29b-41d4-a716-************', 'In Progress'),
('FIU-2025-002', '456 Oak Avenue, Single Family Home', '2025-06-18', '09:15', '550e8400-e29b-41d4-a716-************', 'Completed');

-- Create storage buckets (run these in Supabase dashboard or via API)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('audio-recordings', 'audio-recordings', false);
-- INSERT INTO storage.buckets (id, name, public) VALUES ('exported-documents', 'exported-documents', false);

COMMIT;
